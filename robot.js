// Robot animation states
const STATES = {
  IDLE: 'idle',
  TALKING: 'talking',
  THINKING: 'thinking',
  LISTENING: 'listening'
};

class DaswosRobot {
  constructor() {
    this.state = STATES.IDLE;
    this.angle = 0;
    this.eyeSize = 30;
    this.eyeOffset = 15;
    this.animationTimer = 0;
    this.currentMessage = "";
    this.messageTimer = 0;
    this.showMessage = false;
    this.headBob = 0;
    this.headBobDir = 1;
  }

  setup() {
    // Create canvas
    this.canvas = createCanvas(400, 400);
    this.canvas.parent('robot-container');
    angleMode(DEGREES);
    rectMode(CENTER);
    this.speechBubble = document.getElementById('speech-bubble');
    this.robotMessage = document.getElementById('robot-message');
  }

  draw() {
    background(230, 240, 255);
    translate(width / 2, height / 2 + 50);
    
    // Update animation
    this.update();
    
    // Draw robot
    this.drawRobot();
  }

  update() {
    // Update animation timer
    this.animationTimer++;
    
    // State-specific updates
    switch(this.state) {
      case STATES.TALKING:
        this.headBob += 0.1 * this.headBobDir;
        if (Math.abs(this.headBob) > 3) this.headBobDir *= -1;
        break;
      case STATES.THINKING:
        this.angle = Math.sin(this.animationTimer * 2) * 5;
        break;
      case STATES.LISTENING:
        this.eyeSize = 30 + Math.sin(this.animationTimer * 5) * 5;
        break;
      default: // IDLE
        this.angle = Math.sin(this.animationTimer * 0.5) * 2;
    }
  }
  
  drawRobot() {
    push();
    
    // Head bobbing
    translate(0, this.headBob);
    
    // Draw body (white sphere)
    fill(255);
    stroke(200);
    strokeWeight(2);
    ellipse(0, 20, 120, 140);
    
    // Draw head (blue sphere)
    fill(30, 144, 255);
    noStroke();
    ellipse(0, -60, 100, 100);
    
    // Draw visor (black)
    fill(40, 40, 40);
    arc(0, -60, 80, 60, 0, 180);
    
    // Draw eyes
    fill(100, 200, 255);
    if (this.state === STATES.THINKING) {
      // Squint eyes when thinking
      arc(-this.eyeOffset, -70, 30, 10, 0, 180);
      arc(this.eyeOffset, -70, 30, 10, 0, 180);
    } else {
      // Normal eyes
      ellipse(-this.eyeOffset, -70, this.eyeSize / 2, this.eyeSize);
      ellipse(this.eyeOffset, -70, this.eyeSize / 2, this.eyeSize);
    }
    
    // Draw arms
    stroke(40);
    strokeWeight(10);
    noFill();
    
    // Left arm
    push();
    translate(-60, 0);
    rotate(Math.sin(this.animationTimer * 2) * 10);
    line(0, 0, -40, 20);
    pop();
    
    // Right arm
    push();
    translate(60, 0);
    rotate(Math.sin(this.animationTimer * 2 + 180) * 10);
    line(0, 0, 40, 20);
    pop();
    
    pop();
  }
  
  setState(newState) {
    this.state = newState;
    // Reset animation properties when state changes
    this.animationTimer = 0;
    this.headBob = 0;
    this.eyeSize = 30;
  }
}

// Create a global instance of the robot
let robot;

// p5.js setup function
function setup() {
  robot = new DaswosRobot();
  robot.setup();
}

// p5.js draw function
function draw() {
  robot.draw();
}

// Function to update robot state from the chat interface
function updateRobotState(state) {
  = 'idle') {
  robot.state = state;
  robot.animationTimer = 0; // Reset animation timer when state changes
  
  // Special handling for thinking state
  if (state === 'thinking') {
    robot.eyeSize = 20; // Make eyes smaller when thinking
  } else {
    robot.eyeSize = 30; // Reset eye size
  }
}

// Make robot available globally
window.robot = robot;
window.updateRobotState = updateRobotState;
