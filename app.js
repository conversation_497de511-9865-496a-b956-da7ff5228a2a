// DOM Elements
const userInput = document.getElementById('user-input');
const sendButton = document.getElementById('send-button');
const speechBubble = document.getElementById('speech-bubble');
const robotMessage = document.getElementById('robot-message');

// Animation states
let isRobotTalking = false;
let currentMessage = "";

// Initialize the chat
function initChat() {
    // Show welcome message after a short delay
    setTimeout(() => {
        showMessage("Hello! I'm Daswos. How can I help you today?");
    }, 1000);
    
    // Set up event listeners
    sendButton.addEventListener('click', handleUserMessage);
    userInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            handleUserMessage();
        }
    });
}

// Handle user messages
async function handleUserMessage() {
    const message = userInput.value.trim();
    if (!message) return;
    
    // Add user message to chat (in a real app, you'd display this in a chat log)
    console.log('User:', message);
    
    // Clear input
    userInput.value = '';
    
    // Show typing indicator
    showTypingIndicator();
    
    try {
        // In a real implementation, you would call your OpenAI API here
        // For now, we'll use a simple response
        await simulateApiCall(message);
    } catch (error) {
        console.error('Error:', error);
        showMessage("I'm having trouble connecting right now. Please try again later.");
    }
}

// Simulate API call to OpenAI
async function simulateApiCall(message) {
    // In a real implementation, you would make an actual API call to OpenAI
    // For example:
    /*
    const response = await fetch('YOUR_BACKEND_ENDPOINT', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message })
    });
    const data = await response.json();
    showMessage(data.response);
    */
    
    // Simulate API delay
    return new Promise(resolve => {
        setTimeout(() => {
            // Simple response logic - replace with actual API call
            let response = "I'm not connected to the internet right now. " + 
                         "In a real implementation, I would respond to: \"" + message + "\"";
            showMessage(response);
            resolve();
        }, 1000);
    });
}

// Show typing indicator
function showTypingIndicator() {
    speechBubble.classList.add('visible');
    robotMessage.textContent = '...';
    isRobotTalking = true;
    // Update robot animation state if needed
    if (window.updateRobotState) {
        window.updateRobotState('thinking');
    }
}

// Show message in speech bubble
function showMessage(message) {
    currentMessage = message;
    speechBubble.classList.add('visible');
    robotMessage.textContent = '';
    isRobotTalking = true;
    
    // Update robot animation state
    if (window.updateRobotState) {
        window.updateRobotState('talking');
    }
    
    // Typewriter effect
    let i = 0;
    const speed = 20; // milliseconds per character
    
    function typeWriter() {
        if (i < message.length) {
            robotMessage.textContent += message.charAt(i);
            i++;
            setTimeout(typeWriter, speed);
        } else {
            // When done typing, set a timeout to hide the bubble
            setTimeout(() => {
                isRobotTalking = false;
                if (window.updateRobotState) {
                    window.updateRobotState('idle');
                }
                // Hide bubble after a delay if no new message
                setTimeout(() => {
                    if (!isRobotTalking) {
                        speechBubble.classList.remove('visible');
                    }
                }, 3000);
            }, 1000);
        }
    }
    
    typeWriter();
}

// Initialize chat when the page loads
window.addEventListener('DOMContentLoaded', initChat);

// Expose functions to the global scope for the robot animation
window.showMessage = showMessage;
