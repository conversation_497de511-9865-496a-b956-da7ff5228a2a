body {
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f0f2f5;
    font-family: Arial, sans-serif;
}

.chat-container {
    width: 100%;
    max-width: 800px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 80vh;
}

#robot-container {
    flex: 1;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #e6f3ff 0%, #f8f9fa 100%);
}

#speech-bubble {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    padding: 15px 20px;
    border-radius: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    max-width: 70%;
    opacity: 0;
    transition: opacity 0.3s ease;
    text-align: center;
    z-index: 10;
}

#speech-bubble.visible {
    opacity: 1;
}

.bubble-arrow {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid white;
}

#robot-message {
    font-size: 16px;
    color: #333;
    line-height: 1.4;
}

.chat-controls {
    padding: 20px;
    background: white;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 10px;
}

#user-input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #dee2e6;
    border-radius: 25px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s;
}

#user-input:focus {
    border-color: #4dabf7;
    box-shadow: 0 0 0 3px rgba(77, 171, 247, 0.2);
}

#send-button {
    padding: 0 25px;
    background: #4dabf7;
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.2s;
}

#send-button:hover {
    background: #339af0;
}

#send-button:active {
    transform: translateY(1px);
}

/* Robot canvas styling */
canvas {
    display: block;
    margin: 0 auto;
}
